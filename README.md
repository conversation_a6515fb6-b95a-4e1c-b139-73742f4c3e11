# MutiCamApp
多摄像头应用程序

## 版本历史
2025.01.07, first origin version uploaded by KJ.
2025.01.09，second UI version uploaded by KJ.

## 改进建议

### 1. 软件架构重构
- 使用C++进行软件整体重构，提升性能和稳定性
- 优化内存管理和多线程处理
- 提高图像处理速度和响应性能

### 2. 相机状态监控
- 增加单个相机状态的实时显示功能
- 显示相机连接状态、帧率、温度等关键参数
- 提供相机故障诊断和报警机制

### 3. XYZ载物台控制系统
- 增加XYZ载物台手动控制功能（预留UI界面）
- 实现载物台状态监控和位置反馈
- 支持精密定位和多点位控制
- 提供载物台运动轨迹记录功能

### 4. 物理按键控制
- 增加基于物理按键的一键拍照功能（预留UI界面）
- 通过串口通信实现硬件按键控制
- 支持多种拍照模式和参数预设
- 提供按键状态指示和反馈

### 5. 光栅尺位置显示
- 载物台配备光栅尺，同步显示实际位置（预留UI界面）
- 实时显示X、Y、Z轴精确坐标
- 支持位置校准和零点设置
- 提供位置精度监控和误差补偿

### 6. 模版匹配优化
- 优化模版匹配效果和精度
- 可选择调用Halcon、VisionMaster等商业化算子
- **奖励机制**：如不借助商业化算子直接实现高精度模版匹配，奖励2000元
- 支持多模版匹配和自适应阈值调整
- 提供匹配结果可视化和统计分析

### 7. 像素距离标定功能
- 增加像素与实际物理距离的标定功能
- 支持多种标定方法：单点标定、多点标定、棋盘格标定
- 实现像素坐标到实际坐标的精确转换
- 提供标定精度验证和误差分析
- 支持不同视图的独立标定参数管理
- 自动保存和加载标定参数
- 实时显示测量结果的物理单位（毫米、微米等）
