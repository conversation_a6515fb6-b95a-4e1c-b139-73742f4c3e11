﻿///////////////////////////////////////////////////////////////////////////////
// File generated by HDevelop for HALCON/C++ Version 20.11.1.0
// Non-ASCII strings in this file are encoded in UTF-8.
// 
// Please note that non-ASCII characters in string constants are exported
// as octal codes in order to guarantee that the strings are correctly
// created on all systems, independent on any compiler settings.
// 
// Source files with different encoding should not be mixed in one project.
///////////////////////////////////////////////////////////////////////////////



#ifndef __APPLE__
#  include "HalconCpp.h"
#  include "HDevThread.h"
#  include <windows.h>
#  include <string>
#else
#  ifndef HC_LARGE_IMAGES
#    include <HALCONCpp/HalconCpp.h>
#    include <HALCONCpp/HDevThread.h>
#    include <HALCON/HpThread.h>
#  else
#    include <HALCONCppxl/HalconCpp.h>
#    include <HALCONCppxl/HDevThread.h>
#    include <HALCONxl/HpThread.h>
#  endif
#  include <stdio.h>
#  include <CoreFoundation/CFRunLoop.h>
#endif



using namespace HalconCpp;

// Procedure declarations 
// Chapter: Graphics / Text
// Short Description: This procedure writes one or multiple text messages. 
void disp_message (HTuple hv_WindowHandle, HTuple hv_String, HTuple hv_CoordSystem, 
    HTuple hv_Row, HTuple hv_Column, HTuple hv_Color, HTuple hv_Box);

// Procedures 
// Chapter: Graphics / Text
// Short Description: This procedure writes one or multiple text messages. 
void disp_message (HTuple hv_WindowHandle, HTuple hv_String, HTuple hv_CoordSystem, 
    HTuple hv_Row, HTuple hv_Column, HTuple hv_Color, HTuple hv_Box)
{

  // Local iconic variables

  // Local control variables
  HTuple  hv_GenParamName, hv_GenParamValue;

  //This procedure displays text in a graphics window.
  //
  //Input parameters:
  //WindowHandle: The WindowHandle of the graphics window, where
  //   the message should be displayed.
  //String: A tuple of strings containing the text messages to be displayed.
  //CoordSystem: If set to 'window', the text position is given
  //   with respect to the window coordinate system.
  //   If set to 'image', image coordinates are used.
  //   (This may be useful in zoomed images.)
  //Row: The row coordinate of the desired text position.
  //   You can pass a single value or a tuple of values.
  //   See the explanation below.
  //   Default: 12.
  //Column: The column coordinate of the desired text position.
  //   You can pass a single value or a tuple of values.
  //   See the explanation below.
  //   Default: 12.
  //Color: defines the color of the text as string.
  //   If set to [] or '' the currently set color is used.
  //   If a tuple of strings is passed, the colors are used cyclically
  //   for every text position defined by Row and Column,
  //   or every new text line in case of |Row| == |Column| == 1.
  //Box: A tuple controlling a possible box surrounding the text.
  //   Its entries:
  //   - Box[0]: Controls the box and its color. Possible values:
  //     -- 'true' (Default): An orange box is displayed.
  //     -- 'false': No box is displayed.
  //     -- color string: A box is displayed in the given color, e.g., 'white', '#FF00CC'.
  //   - Box[1] (Optional): Controls the shadow of the box. Possible values:
  //     -- 'true' (Default): A shadow is displayed in
  //               darker orange if Box[0] is not a color and in 'white' otherwise.
  //     -- 'false': No shadow is displayed.
  //     -- color string: A shadow is displayed in the given color, e.g., 'white', '#FF00CC'.
  //
  //It is possible to display multiple text strings in a single call.
  //In this case, some restrictions apply on the
  //parameters String, Row, and Column:
  //They can only have either 1 entry or n entries.
  //Behavior in the different cases:
  //   - Multiple text positions are specified, i.e.,
  //       - |Row| == n, |Column| == n
  //       - |Row| == n, |Column| == 1
  //       - |Row| == 1, |Column| == n
  //     In this case we distinguish:
  //       - |String| == n: Each element of String is displayed
  //                        at the corresponding position.
  //       - |String| == 1: String is displayed n times
  //                        at the corresponding positions.
  //   - Exactly one text position is specified,
  //      i.e., |Row| == |Column| == 1:
  //      Each element of String is display in a new textline.
  //
  //
  //Convert the parameters for disp_text.
  if (0 != (HTuple(int(hv_Row==HTuple())).TupleOr(int(hv_Column==HTuple()))))
  {
    return;
  }
  if (0 != (int(hv_Row==-1)))
  {
    hv_Row = 12;
  }
  if (0 != (int(hv_Column==-1)))
  {
    hv_Column = 12;
  }
  //
  //Convert the parameter Box to generic parameters.
  hv_GenParamName = HTuple();
  hv_GenParamValue = HTuple();
  if (0 != (int((hv_Box.TupleLength())>0)))
  {
    if (0 != (int(HTuple(hv_Box[0])==HTuple("false"))))
    {
      //Display no box
      hv_GenParamName = hv_GenParamName.TupleConcat("box");
      hv_GenParamValue = hv_GenParamValue.TupleConcat("false");
    }
    else if (0 != (int(HTuple(hv_Box[0])!=HTuple("true"))))
    {
      //Set a color other than the default.
      hv_GenParamName = hv_GenParamName.TupleConcat("box_color");
      hv_GenParamValue = hv_GenParamValue.TupleConcat(HTuple(hv_Box[0]));
    }
  }
  if (0 != (int((hv_Box.TupleLength())>1)))
  {
    if (0 != (int(HTuple(hv_Box[1])==HTuple("false"))))
    {
      //Display no shadow.
      hv_GenParamName = hv_GenParamName.TupleConcat("shadow");
      hv_GenParamValue = hv_GenParamValue.TupleConcat("false");
    }
    else if (0 != (int(HTuple(hv_Box[1])!=HTuple("true"))))
    {
      //Set a shadow color other than the default.
      hv_GenParamName = hv_GenParamName.TupleConcat("shadow_color");
      hv_GenParamValue = hv_GenParamValue.TupleConcat(HTuple(hv_Box[1]));
    }
  }
  //Restore default CoordSystem behavior.
  if (0 != (int(hv_CoordSystem!=HTuple("window"))))
  {
    hv_CoordSystem = "image";
  }
  //
  if (0 != (int(hv_Color==HTuple(""))))
  {
    //disp_text does not accept an empty string for Color.
    hv_Color = HTuple();
  }
  //
  DispText(hv_WindowHandle, hv_String, hv_CoordSystem, hv_Row, hv_Column, hv_Color, 
      hv_GenParamName, hv_GenParamValue);
  return;
}

#ifndef NO_EXPORT_MAIN
// Main procedure 
void action()
{

  // Local iconic variables
  HObject  ho_TemplateImage, ho_SearchImage, ho_ModelContours;
  HObject  ho_ContoursAffinTrans, ho_ResultImage;

  // Local control variables
  HTuple  hv_ModelID, hv_Seconds1, hv_Row, hv_Column;
  HTuple  hv_Angle, hv_Scale, hv_Score, hv_Seconds2, hv_MatchingTime;
  HTuple  hv_WindowHandle, hv_i, hv_HomMat2DIdentity, hv_HomMat2DScale;
  HTuple  hv_HomMat2DRotate, hv_HomMat2DTranslate, hv_OutputPath;

  ReadImage(&ho_TemplateImage, "D:/AppData/Documents/match/template/t4.png");
  ReadImage(&ho_SearchImage, "D:/AppData/Documents/match/template/s.png");

  CreateScaledShapeModel(ho_TemplateImage, 5, HTuple(0).TupleRad(), HTuple(360).TupleRad(), 
      HTuple(1).TupleRad(), 0.8, 1.2, 0.01, "auto", "use_polarity", "auto", "auto", 
      &hv_ModelID);


  //***********************************************************************************
  //增加计时功能
  //***********************************************************************************
  //1. 在匹配前，获取开始时间
  CountSeconds(&hv_Seconds1);

  FindScaledShapeModel(ho_SearchImage, hv_ModelID, HTuple(0).TupleRad(), HTuple(360).TupleRad(), 
      0.8, 1.2, 0.5, 0, 0.8, "none", 0, 1, &hv_Row, &hv_Column, &hv_Angle, &hv_Scale, 
      &hv_Score);

  //2. 在匹配后，获取结束时间
  CountSeconds(&hv_Seconds2);

  //3. 计算时间差，并转换为毫秒 (ms)
  hv_MatchingTime = (hv_Seconds2-hv_Seconds1)*1000;


  //***********************************************************************************
  //显示结果
  //***********************************************************************************
  if (HDevWindowStack::IsOpen())
    DispObj(ho_SearchImage, HDevWindowStack::GetActive());
  if (HDevWindowStack::IsOpen())
    hv_WindowHandle = HDevWindowStack::GetActive();

  if (0 != (int((hv_Score.TupleLength())>0)))
  {
    //如果找到了匹配项
    GetShapeModelContours(&ho_ModelContours, hv_ModelID, 1);
    {
    HTuple end_val30 = (hv_Score.TupleLength())-1;
    HTuple step_val30 = 1;
    for (hv_i=0; hv_i.Continue(end_val30, step_val30); hv_i += step_val30)
    {
      HomMat2dIdentity(&hv_HomMat2DIdentity);
      HomMat2dScale(hv_HomMat2DIdentity, HTuple(hv_Scale[hv_i]), HTuple(hv_Scale[hv_i]), 
          0, 0, &hv_HomMat2DScale);
      HomMat2dRotate(hv_HomMat2DScale, HTuple(hv_Angle[hv_i]), 0, 0, &hv_HomMat2DRotate);
      HomMat2dTranslate(hv_HomMat2DRotate, HTuple(hv_Row[hv_i]), HTuple(hv_Column[hv_i]), 
          &hv_HomMat2DTranslate);
      AffineTransContourXld(ho_ModelContours, &ho_ContoursAffinTrans, hv_HomMat2DTranslate);

      if (HDevWindowStack::IsOpen())
        SetColor(HDevWindowStack::GetActive(),"green");
      if (HDevWindowStack::IsOpen())
        SetLineWidth(HDevWindowStack::GetActive(),2);
      if (HDevWindowStack::IsOpen())
        DispObj(ho_ContoursAffinTrans, HDevWindowStack::GetActive());
    }
    }

    //【修改点】在显示数量的同时，显示匹配耗时
    //使用 $'#.2f' 格式化字符串，让时间显示为带两位小数的浮点数
    //disp_message(hv_WindowHandle, HTuple("Found: ")+HTuple(hv_Score.TupleLength())+HTuple(", Time: ")+HTuple(hv_MatchingTime.TupleString("#.2f"))+HTuple(" ms"), 
    //    HTuple("window"), HTuple(100), HTuple(100), HTuple("black"), HTuple("false"));
    printf("Found: %d matches, Time: %.2f ms\n", (int)hv_Score.TupleLength(), (double)hv_MatchingTime);
    
    //【新增功能】保存匹配结果图片（带可视化轮廓）
    // 动态获取项目根目录
    std::string projectRoot;
    try {
      // 获取可执行文件路径
      char buffer[MAX_PATH];
      GetModuleFileNameA(NULL, buffer, MAX_PATH);
      std::string exePath(buffer);
      
      // 从可执行文件路径推导项目根目录
      // 如果可执行文件在build目录下，则项目根目录是上一级
      size_t buildPos = exePath.find("\\build\\");
      if (buildPos != std::string::npos) {
        projectRoot = exePath.substr(0, buildPos);
      } else {
        // 如果不在build目录，使用可执行文件所在目录
        size_t lastSlash = exePath.find_last_of("\\");
        if (lastSlash != std::string::npos) {
          projectRoot = exePath.substr(0, lastSlash);
        } else {
          projectRoot = ".";
        }
      }
    } catch (...) {
      // 如果获取失败，使用相对路径
      projectRoot = "..";
    }
    
    // 构建输出目录路径
    std::string outputDirPath = projectRoot + "\\output";
    HTuple hv_OutputDir = HTuple(outputDirPath.c_str());
    
    try {
      // 尝试创建输出目录（如果不存在）
      HTuple hv_FileExists;
      FileExists(hv_OutputDir, &hv_FileExists);
      if (hv_FileExists.I() == 0) {
        MakeDir(hv_OutputDir);
      }
    } catch (...) {
      // 如果创建目录失败，使用当前目录
      hv_OutputDir = HTuple("output");
    }
    
    // 生成输出文件名（包含匹配数量和耗时）
    hv_OutputPath = hv_OutputDir + HTuple("/match_result_") + HTuple(hv_Score.TupleLength()) + HTuple("_matches_") + HTuple(hv_MatchingTime.TupleString("#.0f")) + HTuple("ms.png");
    
    // 创建临时窗口来渲染带轮廓的图像
    HTuple hv_TempWindow;
    HTuple hv_ImageWidth, hv_ImageHeight;
    
    // 获取原始图像的尺寸
    GetImageSize(ho_SearchImage, &hv_ImageWidth, &hv_ImageHeight);
    
    // 创建临时窗口（不显示），使用原始图像尺寸
    OpenWindow(0, 0, hv_ImageWidth, hv_ImageHeight, 0, "buffer", "", &hv_TempWindow);
    HDevWindowStack::Push(hv_TempWindow);
    
    // 在临时窗口中绘制原始图像
    DispObj(ho_SearchImage, HDevWindowStack::GetActive());
    
    // 绘制所有匹配的轮廓
    SetColor(HDevWindowStack::GetActive(), "green");
    SetLineWidth(HDevWindowStack::GetActive(), 2);
    {
    HTuple end_val31 = (hv_Score.TupleLength())-1;
    HTuple step_val31 = 1;
    for (hv_i=0; hv_i.Continue(end_val31, step_val31); hv_i += step_val31)
    {
      HomMat2dIdentity(&hv_HomMat2DIdentity);
      HomMat2dScale(hv_HomMat2DIdentity, HTuple(hv_Scale[hv_i]), HTuple(hv_Scale[hv_i]), 
          0, 0, &hv_HomMat2DScale);
      HomMat2dRotate(hv_HomMat2DScale, HTuple(hv_Angle[hv_i]), 0, 0, &hv_HomMat2DRotate);
      HomMat2dTranslate(hv_HomMat2DRotate, HTuple(hv_Row[hv_i]), HTuple(hv_Column[hv_i]), 
          &hv_HomMat2DTranslate);
      AffineTransContourXld(ho_ModelContours, &ho_ContoursAffinTrans, hv_HomMat2DTranslate);
      DispObj(ho_ContoursAffinTrans, HDevWindowStack::GetActive());
    }
    }
    
    // 直接保存窗口内容到文件
    DumpWindow(HDevWindowStack::GetActive(), "png", hv_OutputPath);
    
    // 关闭临时窗口
    HDevWindowStack::Pop();
    CloseWindow(hv_TempWindow);
    printf("Result image with visualization saved to: %s\n", hv_OutputPath.S().TextA());
    }
    else
    {
    //如果没有找到
    //【修改点】即使没找到，也显示搜索所花费的时间
    //disp_message(hv_WindowHandle, HTuple("Not Found! Time: ")+HTuple(hv_MatchingTime.TupleString("#.2f"))+HTuple(" ms"), 
    //    HTuple("window"), HTuple(35), HTuple(35), HTuple("black"), HTuple("false"));
    printf("Not Found! Time: %.2f ms\n", (double)hv_MatchingTime);
  }

  //清除模板
  ClearShapeModel(hv_ModelID);
}


#ifndef NO_EXPORT_APP_MAIN

#ifdef __APPLE__
// On OS X systems, we must have a CFRunLoop running on the main thread in
// order for the HALCON graphics operators to work correctly, and run the
// action function in a separate thread. A CFRunLoopTimer is used to make sure
// the action function is not called before the CFRunLoop is running.
// Note that starting with macOS 10.12, the run loop may be stopped when a
// window is closed, so we need to put the call to CFRunLoopRun() into a loop
// of its own.
HTuple      gStartMutex;
H_pthread_t gActionThread;
HBOOL       gTerminate = FALSE;

static void timer_callback(CFRunLoopTimerRef timer, void *info)
{
  UnlockMutex(gStartMutex);
}

static Herror apple_action(void **parameters)
{
  // Wait until the timer has fired to start processing.
  LockMutex(gStartMutex);
  UnlockMutex(gStartMutex);

  try
  {
    action();
  }
  catch (HException &exception)
  {
    fprintf(stderr,"  Error #%u in %s: %s\n", exception.ErrorCode(),
            exception.ProcName().TextA(),
            exception.ErrorMessage().TextA());
  }

  // Tell the main thread to terminate itself.
  LockMutex(gStartMutex);
  gTerminate = TRUE;
  UnlockMutex(gStartMutex);
  CFRunLoopStop(CFRunLoopGetMain());
  return H_MSG_OK;
}

static int apple_main(int argc, char *argv[])
{
  Herror                error;
  CFRunLoopTimerRef     Timer;
  CFRunLoopTimerContext TimerContext = { 0, 0, 0, 0, 0 };

  CreateMutex("type","sleep",&gStartMutex);
  LockMutex(gStartMutex);

  error = HpThreadHandleAlloc(&gActionThread);
  if (H_MSG_OK != error)
  {
    fprintf(stderr,"HpThreadHandleAlloc failed: %d\n", error);
    exit(1);
  }

  error = HpThreadCreate(gActionThread,0,apple_action);
  if (H_MSG_OK != error)
  {
    fprintf(stderr,"HpThreadCreate failed: %d\n", error);
    exit(1);
  }

  Timer = CFRunLoopTimerCreate(kCFAllocatorDefault,
                               CFAbsoluteTimeGetCurrent(),0,0,0,
                               timer_callback,&TimerContext);
  if (!Timer)
  {
    fprintf(stderr,"CFRunLoopTimerCreate failed\n");
    exit(1);
  }
  CFRunLoopAddTimer(CFRunLoopGetCurrent(),Timer,kCFRunLoopCommonModes);

  for (;;)
  {
    HBOOL terminate;

    CFRunLoopRun();

    LockMutex(gStartMutex);
    terminate = gTerminate;
    UnlockMutex(gStartMutex);

    if (terminate)
      break;
  }

  CFRunLoopRemoveTimer(CFRunLoopGetCurrent(),Timer,kCFRunLoopCommonModes);
  CFRelease(Timer);

  error = HpThreadHandleFree(gActionThread);
  if (H_MSG_OK != error)
  {
    fprintf(stderr,"HpThreadHandleFree failed: %d\n", error);
    exit(1);
  }

  ClearMutex(gStartMutex);
  return 0;
}
#endif

int main(int argc, char *argv[])
{
  int ret = 0;

  try
  {
#if defined(_WIN32)
    SetSystem("use_window_thread", "true");
#endif

    // Default settings used in HDevelop (can be omitted)
    // SetSystem("width", 512);   // 注释掉固定尺寸设置，让窗口自适应
    // SetSystem("height", 512);

#ifndef __APPLE__
    action();
#else
    ret = apple_main(argc,argv);
#endif
  }
  catch (HException &exception)
  {
    fprintf(stderr,"  Error #%u in %s: %s\n", exception.ErrorCode(),
            exception.ProcName().TextA(),
            exception.ErrorMessage().TextA());
    ret = 1;
  }
  return ret;
}

#endif


#endif


