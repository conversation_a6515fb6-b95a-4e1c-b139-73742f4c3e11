{"template_creation": {"angle_range": {"begin": -90.0, "end": 90.0, "step": 4.0}, "scale_range": {"begin": 0.9, "end": 1.1, "step": 0.05}, "num_features": 0, "weak_thresh": 30.0, "strong_thresh": 60.0}, "matching": {"final_score_thresh": 0.8, "initial_score_thresh": 0.2, "overlap": 0.4, "mag_thresh": 30.0, "greediness": 0.8, "pyramid_level": 4, "T": 2, "top_k": 10, "strategy": 0, "refinement_search_mode": "fixed", "fixed_angle_window": 20.0, "scale_search_window": 0.1}, "paths": {"model_root": "../template", "class_name": "template", "template_image": "../template/template.png", "source_image": "../template/search.png", "output_image": "../template/result.png"}}